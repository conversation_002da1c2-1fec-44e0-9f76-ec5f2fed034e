#include "../include/test_common.h"
#include <memory>

// Mock基类，模拟TNXEcProAsdu的基本功能
class MockTNXEcProAsdu
{
public:
    MockTNXEcProAsdu(MockModelSeek* pSeekIns, MockLogRecord* pLogRecord)
        : m_pModelSeek(pSeekIns), m_pLogRecord(pLogRecord) {}

    virtual ~MockTNXEcProAsdu() = default;

protected:
    MockModelSeek* m_pModelSeek;
    MockLogRecord* m_pLogRecord;
    u_int8 m_nRii;

    // 模拟日志记录函数
    void RcdTrcLogWithParentClass(const char* msg, const char* className) {
        if (m_pLogRecord) {
            m_pLogRecord->RecordTraceLog(msg, className);
        }
    }

    void RcdErrLogWithParentClass(const char* msg, const char* className) {
        if (m_pLogRecord) {
            m_pLogRecord->RecordErrorLog(msg, className);
        }
    }

    void _SetLogClassName(const char* className) {
        // 模拟设置日志类名
    }
};

// TNXEcProAsdu103GWS的测试实现类
class TestTNXEcProAsdu103GWS : public MockTNXEcProAsdu
{
public:
    TestTNXEcProAsdu103GWS(MockModelSeek* pSeekIns, MockLogRecord* pLogRecord)
        : MockTNXEcProAsdu(pSeekIns, pLogRecord) {
        _SetLogClassName("TNXEcProAsdu103GWS");
        m_nFun = 0;
    }
    
    virtual ~TestTNXEcProAsdu103GWS() = default;
    
    // 实现DirectResFromLocal方法（简化版本，重点测试SettingUp逻辑）
    virtual int DirectResFromLocal(IN PRO_FRAME_BODY* pBody, OUT PRO_FRAME_BODY_LIST& lResult)
    {
        if (!pBody) return EC_PRO_CVT_FAIL;
        
        char cError[255] = "";
        bool bIsRobot = false;
        
        if (pBody->vVarData.size() < 105) {
            RcdErrLogWithParentClass("DirectResFromLocal:召唤通用文件报文的长度不够,可能有错误.", "TNXEcProAsdu103GWS");
            return EC_PRO_CVT_FAIL;
        }
        
        // 获取返回信息标识符
        m_nRii = (u_int8)(pBody->nRii);
        m_nFun = (u_int8)(pBody->nFun);
        
        // 获取文件名和起始发送位置
        char cFileName[255] = "";
        int nBeginSendPos = 0;
        
        if (__GetFileNameAndPosFromProFrameBody(pBody, cFileName, nBeginSendPos) < 0) {
            RcdErrLogWithParentClass("DirectResFromLocal:获取文件名和位置失败", "TNXEcProAsdu103GWS");
            return EC_PRO_CVT_FAIL;
        }
        
        std::string strFullFname = cFileName;
        char* psettingfile = _strstr_nocase((char*)strFullFname.c_str(), "SettingUp");
        
        if (NULL != psettingfile) {
            RcdErrLogWithParentClass("DirectResFromLocal:发现文件名包含SettingUp", "TNXEcProAsdu103GWS");
            
            // 提取SettingUp后面的路径部分
            std::string strName = "";
            int npos = strFullFname.find("SettingUp");
            if (npos != std::string::npos) {
                strName = strFullFname.substr(npos + 9); // 得到SettingUp后面的路径部分
            }
            
            sprintf(cError, "收到召唤定值文件命令,召唤的文件名为:%s,指定的起始位置:%d", cFileName, nBeginSendPos);
            RcdTrcLogWithParentClass(cError, "TNXEcProAsdu103GWS");
            
            if (_GeneralFileHandle_SettingUp((char*)strName.c_str(), nBeginSendPos, pBody, lResult, strFullFname) < 0) {
                return EC_PRO_CVT_FAIL;
            }
            
            RcdTrcLogWithParentClass("发送定值文件数据结束.", "TNXEcProAsdu103GWS");
        } else {
            // 处理其他类型的文件
            char cFilePath[255] = "";
            char _cFileName[255] = "";
            char _cFileExt[255] = "";
            
            if (sy_get_file_name(cFileName, cFilePath, _cFileName, _cFileExt) != 0) {
                RcdErrLogWithParentClass("DirectResFromLocal:召唤通用文件命令中文件名格式不正确或为空.", "TNXEcProAsdu103GWS");
                return EC_PRO_CVT_FAIL;
            }
            
            memset(cFileName, 0, 255);
            sprintf(cFileName, "%s.%s", _cFileName, _cFileExt);
            
            // 其他文件处理逻辑...
        }
        
        return 0;
    }
    
    // 实现_GeneralFileHandle_SettingUp方法
    virtual int _GeneralFileHandle_SettingUp(IN const char* cFileName, IN int nBeginSendPos, 
                                              IN PRO_FRAME_BODY* pBody, OUT PRO_FRAME_BODY_LIST& lResult, 
                                              IN std::string& strFullFname)
    {
        char cError[500] = "";
        
        // 获取文件存放的路径-定值文件路径
        char cTemp[255] = "";
        char cGeneralFilePathName[500] = ""; // 完整路径加文件名
        
        BASIC_CFG_TB CfgTb;
        if (!m_pModelSeek->GetBasicCfg(CfgTb)) {
            RcdErrLogWithParentClass("_GeneralFileHandle_SettingUp:获取系统基本配置表信息失败。", "TNXEcProAsdu103GWS");
            return -1;
        }
        
        if (sy_format_file_path(CfgTb.str_file_path.c_str(), cTemp) < 0) {
            sprintf(cError, "_GeneralFileHandle_SettingUp():格式化定值文件路径[%s]出错.", CfgTb.str_file_path.c_str());
            RcdErrLogWithParentClass(cError, "TNXEcProAsdu103GWS");
            return -1;
        }
        
        // 拼接路径：基础路径 + "/SettingUp/" + 设备addr地址 + 文件名
        sprintf(cGeneralFilePathName, "%s/SettingUp/%d%s", cTemp, pBody->nAddr, cFileName);
        
        sprintf(cError, "_GeneralFileHandle_SettingUp:搜索路径[%s],设备addr地址[%d]", cGeneralFilePathName, pBody->nAddr);
        RcdTrcLogWithParentClass(cError, "TNXEcProAsdu103GWS");
        
        // 判断文件是否存在
        FILE_PROPERTY_INF FileInfo;
        if (sy_get_file_property(cGeneralFilePathName, &FileInfo) != 0) {
            sprintf(cError, "_GeneralFileHandle_SettingUp:文件[%s]不存在或无法访问", cGeneralFilePathName);
            RcdErrLogWithParentClass(cError, "TNXEcProAsdu103GWS");
            return -1;
        }
        
        // 模拟文件传输处理
        return __ProcessFileTransfer(cGeneralFilePathName, nBeginSendPos, pBody, lResult, FileInfo);
    }
    
private:
    u_int8 m_nFun;
    
    // 辅助方法：从协议帧体中获取文件名和起始位置
    int __GetFileNameAndPosFromProFrameBody(PRO_FRAME_BODY* pBody, char* fileName, int& beginPos)
    {
        if (!pBody || !fileName) return -1;
        
        if (pBody->vVarData.size() < 105) {
            return -1;
        }
        
        // 模拟从可变数据中提取文件名（假设从字节20开始，最大64字节）
        memset(fileName, 0, 255);
        size_t nameLen = std::min(size_t(64), pBody->vVarData.size() - 20);
        memcpy(fileName, &pBody->vVarData[20], nameLen);
        
        // 确保字符串以null结尾
        fileName[nameLen] = '\0';
        
        // 模拟从可变数据中提取起始位置（假设从字节100开始，4字节）
        if (pBody->vVarData.size() >= 104) {
            memcpy(&beginPos, &pBody->vVarData[100], 4);
        } else {
            beginPos = 0;
        }
        
        return 0;
    }
    
    // 辅助方法：处理文件传输
    int __ProcessFileTransfer(const char* filePath, int beginPos, PRO_FRAME_BODY* pBody, 
                              PRO_FRAME_BODY_LIST& lResult, const FILE_PROPERTY_INF& fileInfo)
    {
        // 创建文件传输响应帧体
        PRO_FRAME_BODY responseBody;
        responseBody.nType = 0x67; // ASDU103类型
        responseBody.nVsq = 0x81;
        responseBody.nCot = 0x07; // 激活确认
        responseBody.nSubstationAdd = pBody->nSubstationAdd;
        responseBody.nAddr = pBody->nAddr;
        responseBody.nCpu = pBody->nCpu;
        responseBody.nZone = pBody->nZone;
        responseBody.nFun = m_nFun;
        responseBody.nInf = 0x00;
        responseBody.nRii = m_nRii;
        
        // 模拟文件数据传输
        // 实际实现中应该读取文件内容并分段传输
        std::string mockFileContent = "Mock setting file content for testing";
        
        // 计算传输的数据段
        size_t contentSize = mockFileContent.size();
        size_t startPos = std::min((size_t)beginPos, contentSize);
        size_t remainingSize = contentSize - startPos;
        size_t chunkSize = std::min(remainingSize, size_t(200)); // 每次最多传输200字节
        
        // 构造响应数据
        responseBody.vVarData.resize(chunkSize + 8); // 数据 + 头部信息
        
        // 设置文件信息头部
        memcpy(&responseBody.vVarData[0], &fileInfo.nFileSize, 4);
        memcpy(&responseBody.vVarData[4], &startPos, 4);
        
        // 复制文件内容
        if (chunkSize > 0) {
            memcpy(&responseBody.vVarData[8], mockFileContent.c_str() + startPos, chunkSize);
        }
        
        lResult.push_back(responseBody);
        
        char cError[255];
        sprintf(cError, "__ProcessFileTransfer:成功处理文件传输,文件大小:%d,起始位置:%d,传输大小:%zu", 
                fileInfo.nFileSize, beginPos, chunkSize);
        RcdTrcLogWithParentClass(cError, "TNXEcProAsdu103GWS");
        
        return 0;
    }
};

// 测试类
class TNXEcProAsdu103GWSTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        mockModelSeek = std::make_unique<MockModelSeek>();
        mockLogRecord = std::make_unique<MockLogRecord>();
        testAsdu103 = std::make_unique<TestTNXEcProAsdu103GWS>(mockModelSeek.get(), mockLogRecord.get());
        
        // 创建测试文件结构
        TestUtils::CreateTestFileStructure("/tmp/test_files");
    }
    
    void TearDown() override
    {
        // 清理测试文件
        TestUtils::CleanupTestFiles("/tmp/test_files");
    }
    
    std::unique_ptr<MockModelSeek> mockModelSeek;
    std::unique_ptr<MockLogRecord> mockLogRecord;
    std::unique_ptr<TestTNXEcProAsdu103GWS> testAsdu103;
};

// 测试DirectResFromLocal方法处理SettingUp关键字
TEST_F(TNXEcProAsdu103GWSTest, DirectResFromLocal_SettingUp_Success)
{
    // 准备测试数据
    PRO_FRAME_BODY testBody = TestUtils::CreateTestFrameBody(1, "SettingUp/setting1.txt");
    testBody.vVarData.resize(105, 0); // 确保长度足够
    
    // 设置文件名到可变数据中
    std::string fileName = "SettingUp/setting1.txt";
    memcpy(&testBody.vVarData[20], fileName.c_str(), fileName.length());
    
    // 设置起始位置
    int beginPos = 0;
    memcpy(&testBody.vVarData[100], &beginPos, 4);
    
    PRO_FRAME_BODY_LIST result;
    
    // 设置Mock期望
    BASIC_CFG_TB cfg;
    cfg.str_file_path = "/tmp/test_files";
    EXPECT_CALL(*mockModelSeek, GetBasicCfg(::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgReferee<0>(cfg), ::testing::Return(true)));
    
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(1));
    
    EXPECT_CALL(*mockLogRecord, RecordErrorLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(1));
    
    // 执行测试
    int ret = testAsdu103->DirectResFromLocal(&testBody, result);
    
    // 验证结果
    EXPECT_EQ(ret, 0);
    EXPECT_GT(result.size(), 0);
    
    // 验证返回的帧体基本信息
    for (const auto& body : result) {
        TestUtils::VerifyFrameBodyBasics(body, 0x67);
        EXPECT_EQ(body.nCot, 0x07); // 激活确认
        EXPECT_GT(body.vVarData.size(), 8); // 应该包含文件头部信息
    }
}

// 测试_GeneralFileHandle_SettingUp方法
TEST_F(TNXEcProAsdu103GWSTest, GeneralFileHandle_SettingUp_Success)
{
    // 准备测试数据
    PRO_FRAME_BODY testBody = TestUtils::CreateTestFrameBody(1);
    PRO_FRAME_BODY_LIST result;
    std::string fullFileName = "SettingUp/setting1.txt";
    
    // 设置Mock期望
    BASIC_CFG_TB cfg;
    cfg.str_file_path = "/tmp/test_files";
    EXPECT_CALL(*mockModelSeek, GetBasicCfg(::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgReferee<0>(cfg), ::testing::Return(true)));
    
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(1));
    
    // 执行测试
    int ret = testAsdu103->_GeneralFileHandle_SettingUp("/setting1.txt", 0, &testBody, result, fullFileName);
    
    // 验证结果
    EXPECT_EQ(ret, 0);
    EXPECT_GT(result.size(), 0);
    
    // 验证文件传输响应
    const auto& responseBody = result[0];
    EXPECT_EQ(responseBody.nType, 0x67);
    EXPECT_EQ(responseBody.nCot, 0x07);
    EXPECT_GT(responseBody.vVarData.size(), 8); // 应该包含文件头部和数据
}

// 测试配置获取失败的情况
TEST_F(TNXEcProAsdu103GWSTest, GeneralFileHandle_SettingUp_ConfigFail)
{
    // 准备测试数据
    PRO_FRAME_BODY testBody = TestUtils::CreateTestFrameBody(1);
    PRO_FRAME_BODY_LIST result;
    std::string fullFileName = "SettingUp/setting1.txt";
    
    // 设置Mock期望 - 配置获取失败
    EXPECT_CALL(*mockModelSeek, GetBasicCfg(::testing::_))
        .WillOnce(::testing::Return(false));
    
    EXPECT_CALL(*mockLogRecord, RecordErrorLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(1));
    
    // 执行测试
    int ret = testAsdu103->_GeneralFileHandle_SettingUp("/setting1.txt", 0, &testBody, result, fullFileName);
    
    // 验证结果
    EXPECT_EQ(ret, -1);
    EXPECT_EQ(result.size(), 0);
}

// 测试报文长度不够的情况
TEST_F(TNXEcProAsdu103GWSTest, DirectResFromLocal_InsufficientLength)
{
    // 准备测试数据 - 长度不够
    PRO_FRAME_BODY testBody = TestUtils::CreateTestFrameBody(1, "test.txt");
    testBody.vVarData.resize(50); // 长度不够105
    PRO_FRAME_BODY_LIST result;
    
    EXPECT_CALL(*mockLogRecord, RecordErrorLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(1));
    
    // 执行测试
    int ret = testAsdu103->DirectResFromLocal(&testBody, result);
    
    // 验证结果
    EXPECT_EQ(ret, EC_PRO_CVT_FAIL);
    EXPECT_EQ(result.size(), 0);
}

// 测试非SettingUp文件名的处理
TEST_F(TNXEcProAsdu103GWSTest, DirectResFromLocal_NonSettingUp)
{
    // 准备测试数据 - 不包含SettingUp关键字
    PRO_FRAME_BODY testBody = TestUtils::CreateTestFrameBody(1, "normal_file.txt");
    testBody.vVarData.resize(105, 0);
    
    // 设置文件名到可变数据中
    std::string fileName = "normal_file.txt";
    memcpy(&testBody.vVarData[20], fileName.c_str(), fileName.length());
    
    PRO_FRAME_BODY_LIST result;
    
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(0));
    
    // 执行测试
    int ret = testAsdu103->DirectResFromLocal(&testBody, result);
    
    // 验证结果 - 应该走其他处理分支
    EXPECT_EQ(ret, 0);
}
