#include "../include/test_common.h"
#include <memory>

// Mock基类，模拟TNXEcProAsdu的基本功能
class MockTNXEcProAsdu
{
public:
    MockTNXEcProAsdu(MockModelSeek* pSeekIns, MockLogRecord* pLogRecord)
        : m_pModelSeek(pSeekIns), m_pLogRecord(pLogRecord) {}

    virtual ~MockTNXEcProAsdu() = default;

protected:
    MockModelSeek* m_pModelSeek;
    MockLogRecord* m_pLogRecord;
    u_int8 m_nRii;

    // 模拟日志记录函数
    void RcdTrcLogWithParentClass(const char* msg, const char* className) {
        if (m_pLogRecord) {
            m_pLogRecord->RecordTraceLog(msg, className);
        }
    }

    void RcdErrLogWithParentClass(const char* msg, const char* className) {
        if (m_pLogRecord) {
            m_pLogRecord->RecordErrorLog(msg, className);
        }
    }

    void _SetLogClassName(const char* className) {
        // 模拟设置日志类名
    }
};

// TNXEcProAsdu106GWS的测试实现类
class TestTNXEcProAsdu106GWS : public MockTNXEcProAsdu
{
public:
    TestTNXEcProAsdu106GWS(MockModelSeek* pSeekIns, MockLogRecord* pLogRecord)
        : MockTNXEcProAsdu(pSeekIns, pLogRecord), m_pEventMsg(nullptr) {
        _SetLogClassName("TNXEcProAsdu106GWS");
    }
    
    virtual ~TestTNXEcProAsdu106GWS() {
        m_pEventMsg = nullptr;
    }
    
    // 实现ConvertEventMsgToPro方法
    virtual int ConvertEventMsgToPro(IN NX_EVENT_MESSAGE* pMsg, OUT PRO_FRAME_BODY_LIST& lBody)
    {
        char cError[255] = "";
        int nRet = 0;
        
        m_pEventMsg = pMsg;
        
        switch(pMsg->n_msg_type)
        {
        case NX_IED_EVENT_FILE_REPORT:  // 文件生成事件
            nRet = CvtEventFileReport(lBody);
            break;
        default:
            sprintf(cError, "ConvertEventMsgToPro()中暂不支持n_msg_type=%d的消息处理", pMsg->n_msg_type);
            RcdErrLogWithParentClass(cError, "TNXEcProAsdu106GWS");
            nRet = EC_PRO_CVT_NOSUPPORT;
            break;
        }
        
        m_pEventMsg = nullptr;
        return nRet;
    }
    
    // 实现CvtEventFileReport方法
    virtual int CvtEventFileReport(OUT PRO_FRAME_BODY_LIST& lBody)
    {
        char cError[255] = "";
        
        // 获得变电站地址
        const SUBSTATION_TB* pStation = m_pModelSeek->GetSubStationBasicCfg();
        if (pStation == nullptr) {
            RcdErrLogWithParentClass("CvtEventFileReport:获取变电站基本配置失败.", "TNXEcProAsdu106GWS");
            return EC_PRO_CVT_FAIL;
        }
        
        // 获得IED配置
        const IED_TB* pIed = m_pModelSeek->GetIedBasicCfg(m_pEventMsg->n_event_obj);
        if (pIed == nullptr) {
            sprintf(cError, "CvtEventFileReport:获取IED基本配置失败,IED编号:%d", m_pEventMsg->n_event_obj);
            RcdErrLogWithParentClass(cError, "TNXEcProAsdu106GWS");
            return EC_PRO_CVT_FAIL;
        }
        
        // 构建ASDU106信息结构
        ASDU106_INFO Asdu106Info;
        
        // 地址信息
        Asdu106Info.Addr.nSubstationAdd = pStation->n_outaddr103;
        Asdu106Info.Addr.nAddr = pIed->n_outaddr103;
        Asdu106Info.Addr.nCpu = 0; // 默认CPU号
        Asdu106Info.Addr.nZone = 0; // 默认定值区号
        
        // 信息体 - ASDU106固定值
        Asdu106Info.InfoObj.nFun = 0xA2; // 功能类型：文件生成通知
        Asdu106Info.InfoObj.nInf = 0x00; // 信息序号
        Asdu106Info.InfoObj.nDpi = 0;    // DPI值
        
        // 文件类型：1-定值文件
        Asdu106Info.nFileType = 1;
        
        // 时间信息
        Asdu106Info.InfoTime.nInfoHappenUtc = m_pEventMsg->n_send_utctm;
        Asdu106Info.InfoTime.nInfoHappenMs = 0;
        Asdu106Info.InfoTime.nInfoRcvUtc = m_pEventMsg->n_send_utctm;
        Asdu106Info.InfoTime.nInfoRcvMs = 0;
        
        // 文件名处理 - 从list_subfields中获取文件信息
        std::string strFileName = "";
        
        // 检查list_subfields是否有数据
        if (m_pEventMsg->list_subfields.size() <= 0) {
            sprintf(cError, "CvtEventFileReport:NX事件消息的list_subfields为空,IED编号:%d", m_pEventMsg->n_event_obj);
            RcdErrLogWithParentClass(cError, "TNXEcProAsdu106GWS");
            return EC_PRO_CVT_FAIL;
        }
        
        // 遍历list_subfields查找文件名信息
        auto ite = m_pEventMsg->list_subfields.begin();
        while (ite != m_pEventMsg->list_subfields.end()) {
            if (strlen(ite->c_field_name) > 0) {
                strFileName = ite->c_field_name;
                sprintf(cError, "CvtEventFileReport:使用c_field_name作为文件名:%s", strFileName.c_str());
                RcdTrcLogWithParentClass(cError, "TNXEcProAsdu106GWS");
                break;
            }
            ++ite;
        }
        
        // 最终验证文件名是否获取成功
        if (strFileName.empty()) {
            sprintf(cError, "CvtEventFileReport:无法从NX报文list_subfields获取文件名,IED编号:%d,字段数量:%zu",
                    m_pEventMsg->n_event_obj, m_pEventMsg->list_subfields.size());
            RcdErrLogWithParentClass(cError, "TNXEcProAsdu106GWS");
            return EC_PRO_CVT_FAIL;
        }
        
        Asdu106Info.strWavFileName = strFileName;
        
        // 格式化ASDU106报文体
        if (FormatAsdu106Body(Asdu106Info, lBody) < 0) {
            RcdErrLogWithParentClass("CvtEventFileReport:格式化ASDU106报文体失败.", "TNXEcProAsdu106GWS");
            return EC_PRO_CVT_FAIL;
        }
        
        if (lBody.size() <= 0) {
            sprintf(cError, "CvtEventFileReport():转换文件生成事件消息失败,IED编号:%d", m_pEventMsg->n_event_obj);
            RcdErrLogWithParentClass(cError, "TNXEcProAsdu106GWS");
            return EC_PRO_CVT_FAIL;
        } else {
            sprintf(cError, "CvtEventFileReport():转换文件生成事件消息为%zu条ASDU106,IED编号:%d,文件名:%s",
                    lBody.size(), m_pEventMsg->n_event_obj, Asdu106Info.strWavFileName.c_str());
            RcdTrcLogWithParentClass(cError, "TNXEcProAsdu106GWS");
        }
        
        return 0;
    }
    
    // 实现FormatAsdu106Body方法
    virtual int FormatAsdu106Body(IN ASDU106_INFO& Asdu106Info, OUT PRO_FRAME_BODY_LIST& lBody, IN int nReserve = 0)
    {
        PRO_FRAME_BODY FrameBody;
        char cError[255] = "";
        
        // 固定部分
        FrameBody.nType = 0x6A;  // 类型标识：6AH
        FrameBody.nVsq = 0x81;   // 可变结构限定词：81H
        FrameBody.nCot = 0x01;   // 传送原因：01H (自发)
        FrameBody.nSubstationAdd = Asdu106Info.Addr.nSubstationAdd;
        FrameBody.nAddr = Asdu106Info.Addr.nAddr;
        FrameBody.nCpu = Asdu106Info.Addr.nCpu;
        FrameBody.nZone = Asdu106Info.Addr.nZone;
        FrameBody.nFun = Asdu106Info.InfoObj.nFun;
        FrameBody.nInf = Asdu106Info.InfoObj.nInf;
        
        // 可变部分
        // 文件类型 (1字节)
        FrameBody.vVarData.push_back(Asdu106Info.nFileType);
        
        // 文件生成时间 (CP56Time2a格式，7字节)
        // 模拟时间转换
        std::string strHappenTm = __ConvertTimeToCP56Format(Asdu106Info.InfoTime.nInfoHappenUtc, 
                                                             Asdu106Info.InfoTime.nInfoHappenMs);
        FrameBody.vVarData.insert(FrameBody.vVarData.end(), strHappenTm.begin(), strHappenTm.end());
        
        // 文件名 (256字节，带后缀，未使用字节补0)
        std::string strFileName = Asdu106Info.strWavFileName;
        if (strFileName.length() > 255) {
            snprintf(cError, sizeof(cError), "FormatAsdu106Body:文件名长度[%zu]超过255字节限制,将被截断", strFileName.length());
            RcdErrLogWithParentClass(cError, "TNXEcProAsdu106GWS");
            strFileName = strFileName.substr(0, 255);
        }
        
        // 使用resize方法预分配256字节空间
        size_t nCurrentSize = FrameBody.vVarData.size();
        FrameBody.vVarData.resize(nCurrentSize + 256, 0);
        if (!strFileName.empty()) {
            memcpy(&(FrameBody.vVarData[nCurrentSize]), strFileName.c_str(), strFileName.length());
        }
        
        // 附加信息 (40字节，未使用字节补0)
        std::string strReserve = Asdu106Info.strReserve;
        if (strReserve.length() > 39) {
            snprintf(cError, sizeof(cError), "FormatAsdu106Body:附加信息长度[%zu]超过39字节限制,将被截断", strReserve.length());
            RcdErrLogWithParentClass(cError, "TNXEcProAsdu106GWS");
            strReserve = strReserve.substr(0, 39);
        }
        
        // 使用resize方法预分配40字节空间
        nCurrentSize = FrameBody.vVarData.size();
        FrameBody.vVarData.resize(nCurrentSize + 40, 0);
        if (!strReserve.empty()) {
            memcpy(&(FrameBody.vVarData[nCurrentSize]), strReserve.c_str(), strReserve.length());
        }
        
        // 添加到输出列表
        lBody.push_back(FrameBody);
        
        snprintf(cError, sizeof(cError), "FormatAsdu106Body:成功格式化ASDU106报文,addr=%d cpu=%d 文件名:%.50s 文件类型:%d 报文长度:%zu",
                Asdu106Info.Addr.nAddr, Asdu106Info.Addr.nCpu, strFileName.c_str(),
                Asdu106Info.nFileType, FrameBody.vVarData.size());
        RcdTrcLogWithParentClass(cError, "TNXEcProAsdu106GWS");
        
        return 0;
    }
    
private:
    NX_EVENT_MESSAGE* m_pEventMsg;
    
    // 辅助方法：将时间转换为CP56Time2a格式
    std::string __ConvertTimeToCP56Format(u_int32 utcTime, u_int16 ms)
    {
        // 模拟CP56Time2a格式转换，实际应该使用CTimeConvert类
        std::string timeStr;
        timeStr.resize(7, 0); // CP56Time2a是7字节
        
        // 简化实现：将UTC时间和毫秒值填入
        memcpy(&timeStr[0], &utcTime, 4);
        memcpy(&timeStr[4], &ms, 2);
        timeStr[6] = 0; // 质量描述符
        
        return timeStr;
    }
};

// 测试类
class TNXEcProAsdu106GWSTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        mockModelSeek = std::make_unique<MockModelSeek>();
        mockLogRecord = std::make_unique<MockLogRecord>();
        testAsdu106 = std::make_unique<TestTNXEcProAsdu106GWS>(mockModelSeek.get(), mockLogRecord.get());
    }
    
    void TearDown() override
    {
        // 清理资源
    }
    
    std::unique_ptr<MockModelSeek> mockModelSeek;
    std::unique_ptr<MockLogRecord> mockLogRecord;
    std::unique_ptr<TestTNXEcProAsdu106GWS> testAsdu106;
};

// 测试ConvertEventMsgToPro方法处理文件生成事件
TEST_F(TNXEcProAsdu106GWSTest, ConvertEventMsgToPro_FileReport_Success)
{
    // 准备测试数据
    NX_EVENT_MESSAGE testMsg = TestUtils::CreateTestEventMessage(NX_IED_EVENT_FILE_REPORT, 1, "setting1.txt");
    PRO_FRAME_BODY_LIST result;
    
    // 设置Mock期望
    SUBSTATION_TB station;
    station.n_outaddr103 = 1;
    EXPECT_CALL(*mockModelSeek, GetSubStationBasicCfg())
        .WillOnce(::testing::Return(&station));
    
    IED_TB ied;
    ied.n_outaddr103 = 1;
    EXPECT_CALL(*mockModelSeek, GetIedBasicCfg(1))
        .WillOnce(::testing::Return(&ied));
    
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(1));
    
    // 执行测试
    int ret = testAsdu106->ConvertEventMsgToPro(&testMsg, result);
    
    // 验证结果
    EXPECT_EQ(ret, 0);
    EXPECT_GT(result.size(), 0);
    
    // 验证返回的帧体基本信息
    const auto& body = result[0];
    EXPECT_EQ(body.nType, 0x6A);
    EXPECT_EQ(body.nVsq, 0x81);
    EXPECT_EQ(body.nCot, 0x01);
    EXPECT_EQ(body.nFun, 0xA2);
    EXPECT_GT(body.vVarData.size(), 0);
}

// 测试CvtEventFileReport方法
TEST_F(TNXEcProAsdu106GWSTest, CvtEventFileReport_Success)
{
    // 准备测试数据
    NX_EVENT_MESSAGE testMsg = TestUtils::CreateTestEventMessage(NX_IED_EVENT_FILE_REPORT, 1, "config.xml");
    PRO_FRAME_BODY_LIST result;
    
    // 设置Mock期望
    SUBSTATION_TB station;
    station.n_outaddr103 = 2;
    EXPECT_CALL(*mockModelSeek, GetSubStationBasicCfg())
        .WillOnce(::testing::Return(&station));
    
    IED_TB ied;
    ied.n_outaddr103 = 3;
    EXPECT_CALL(*mockModelSeek, GetIedBasicCfg(1))
        .WillOnce(::testing::Return(&ied));
    
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(1));
    
    // 手动调用CvtEventFileReport（模拟内部调用）
    // 由于m_pEventMsg是私有成员，我们通过ConvertEventMsgToPro来间接测试
    int ret = testAsdu106->ConvertEventMsgToPro(&testMsg, result);
    
    // 验证结果
    EXPECT_EQ(ret, 0);
    EXPECT_GT(result.size(), 0);
    
    // 验证地址信息
    const auto& body = result[0];
    EXPECT_EQ(body.nSubstationAdd, 2);
    EXPECT_EQ(body.nAddr, 3);
}

// 测试FormatAsdu106Body方法
TEST_F(TNXEcProAsdu106GWSTest, FormatAsdu106Body_Success)
{
    // 准备测试数据
    ASDU106_INFO testInfo = TestUtils::CreateTestAsdu106Info("test_file.dat");
    testInfo.strReserve = "test reserve info";
    PRO_FRAME_BODY_LIST result;
    
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(1));
    
    // 执行测试
    int ret = testAsdu106->FormatAsdu106Body(testInfo, result);
    
    // 验证结果
    EXPECT_EQ(ret, 0);
    EXPECT_EQ(result.size(), 1);
    
    // 验证帧体结构
    const auto& body = result[0];
    EXPECT_EQ(body.nType, 0x6A);
    EXPECT_EQ(body.nVsq, 0x81);
    EXPECT_EQ(body.nCot, 0x01);
    EXPECT_EQ(body.nFun, 0xA2);
    
    // 验证可变数据长度：1(文件类型) + 7(时间) + 256(文件名) + 40(附加信息) = 304字节
    EXPECT_EQ(body.vVarData.size(), 304);
    
    // 验证文件类型
    EXPECT_EQ(body.vVarData[0], 1);
}

// 测试不支持的消息类型
TEST_F(TNXEcProAsdu106GWSTest, ConvertEventMsgToPro_UnsupportedType)
{
    // 准备测试数据 - 不支持的消息类型
    NX_EVENT_MESSAGE testMsg = TestUtils::CreateTestEventMessage(9999, 1, "test.txt");
    PRO_FRAME_BODY_LIST result;
    
    EXPECT_CALL(*mockLogRecord, RecordErrorLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(1));
    
    // 执行测试
    int ret = testAsdu106->ConvertEventMsgToPro(&testMsg, result);
    
    // 验证结果
    EXPECT_EQ(ret, EC_PRO_CVT_NOSUPPORT);
    EXPECT_EQ(result.size(), 0);
}

// 测试变电站配置获取失败
TEST_F(TNXEcProAsdu106GWSTest, CvtEventFileReport_StationConfigFail)
{
    // 准备测试数据
    NX_EVENT_MESSAGE testMsg = TestUtils::CreateTestEventMessage(NX_IED_EVENT_FILE_REPORT, 1, "test.txt");
    PRO_FRAME_BODY_LIST result;
    
    // 设置Mock期望 - 变电站配置获取失败
    EXPECT_CALL(*mockModelSeek, GetSubStationBasicCfg())
        .WillOnce(::testing::Return(nullptr));
    
    EXPECT_CALL(*mockLogRecord, RecordErrorLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(1));
    
    // 手动调用CvtEventFileReport（通过ConvertEventMsgToPro间接调用）
    int ret = testAsdu106->ConvertEventMsgToPro(&testMsg, result);
    
    // 验证结果
    EXPECT_EQ(ret, EC_PRO_CVT_FAIL);
    EXPECT_EQ(result.size(), 0);
}

// 测试空文件名的情况
TEST_F(TNXEcProAsdu106GWSTest, CvtEventFileReport_EmptyFileName)
{
    // 准备测试数据 - 空的子字段列表
    NX_EVENT_MESSAGE testMsg = TestUtils::CreateTestEventMessage(NX_IED_EVENT_FILE_REPORT, 1, "");
    testMsg.list_subfields.clear(); // 清空子字段列表
    PRO_FRAME_BODY_LIST result;
    
    // 设置Mock期望
    SUBSTATION_TB station;
    EXPECT_CALL(*mockModelSeek, GetSubStationBasicCfg())
        .WillOnce(::testing::Return(&station));
    
    IED_TB ied;
    EXPECT_CALL(*mockModelSeek, GetIedBasicCfg(1))
        .WillOnce(::testing::Return(&ied));
    
    EXPECT_CALL(*mockLogRecord, RecordErrorLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(1));
    
    // 手动调用CvtEventFileReport（通过ConvertEventMsgToPro间接调用）
    int ret = testAsdu106->ConvertEventMsgToPro(&testMsg, result);
    
    // 验证结果
    EXPECT_EQ(ret, EC_PRO_CVT_FAIL);
    EXPECT_EQ(result.size(), 0);
}

// 测试长文件名截断
TEST_F(TNXEcProAsdu106GWSTest, FormatAsdu106Body_LongFileName)
{
    // 准备测试数据 - 超长文件名
    ASDU106_INFO testInfo = TestUtils::CreateTestAsdu106Info("");
    testInfo.strWavFileName = std::string(300, 'a'); // 300个字符的文件名
    testInfo.strReserve = std::string(50, 'b'); // 50个字符的附加信息
    PRO_FRAME_BODY_LIST result;
    
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(1));
    
    EXPECT_CALL(*mockLogRecord, RecordErrorLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(2)); // 文件名和附加信息都会被截断
    
    // 执行测试
    int ret = testAsdu106->FormatAsdu106Body(testInfo, result);
    
    // 验证结果
    EXPECT_EQ(ret, 0);
    EXPECT_EQ(result.size(), 1);
    
    // 验证数据长度仍然正确
    const auto& body = result[0];
    EXPECT_EQ(body.vVarData.size(), 304); // 1 + 7 + 256 + 40
}
