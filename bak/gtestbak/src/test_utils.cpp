#include "../include/test_common.h"
#include <filesystem>
#include <fstream>
#include <iostream>

// 创建测试用的PRO_FRAME_BODY
PRO_FRAME_BODY TestUtils::CreateTestFrameBody(u_int16 addr, const std::string& fileName)
{
    PRO_FRAME_BODY body;
    body.nType = 0x65; // ASDU101类型
    body.nVsq = 0x81;
    body.nCot = 0x06; // 激活
    body.nSubstationAdd = 1;
    body.nAddr = addr;
    body.nCpu = 0;
    body.nZone = 0;
    body.nFun = 0xFE; // 文件传输
    body.nInf = 0x00;
    body.nRii = 0x01;
    
    // 构造可变数据部分，模拟召唤通用文件列表的数据格式
    // 前115字节是固定格式，包含时间范围和文件名等信息
    body.vVarData.resize(115, 0);
    
    // 设置时间范围 (假设从字节0-7是开始时间，8-15是结束时间)
    u_int32 startTime = 1640995200; // 2022-01-01 00:00:00
    u_int32 endTime = 1672531200;   // 2023-01-01 00:00:00
    
    memcpy(&body.vVarData[0], &startTime, 4);
    memcpy(&body.vVarData[8], &endTime, 4);
    
    // 设置文件名 (假设从字节20开始是文件名，最大64字节)
    if (!fileName.empty()) {
        size_t nameLen = std::min(fileName.length(), size_t(64));
        memcpy(&body.vVarData[20], fileName.c_str(), nameLen);
    }
    
    return body;
}

// 创建测试用的NX_EVENT_MESSAGE
NX_EVENT_MESSAGE TestUtils::CreateTestEventMessage(u_int32 msgType, u_int32 iedId, const std::string& fileName)
{
    NX_EVENT_MESSAGE msg;
    msg.n_msg_type = msgType;
    msg.n_event_obj = iedId;
    msg.n_send_utctm = 1640995200; // 2022-01-01 00:00:00
    
    // 添加文件名到子字段列表
    if (!fileName.empty()) {
        NX_EVENT_MSG_SUBFIELD subfield;
        strncpy(subfield.c_field_name, fileName.c_str(), sizeof(subfield.c_field_name) - 1);
        msg.list_subfields.push_back(subfield);
    }
    
    return msg;
}

// 创建测试用的ASDU106_INFO
ASDU106_INFO TestUtils::CreateTestAsdu106Info(const std::string& fileName)
{
    ASDU106_INFO info;
    
    // 地址信息
    info.Addr.nSubstationAdd = 1;
    info.Addr.nAddr = 1;
    info.Addr.nCpu = 0;
    info.Addr.nZone = 0;
    
    // 信息体
    info.InfoObj.nFun = 0xA2; // 文件生成通知
    info.InfoObj.nInf = 0x00;
    info.InfoObj.nDpi = 0;
    
    // 文件类型
    info.nFileType = 1; // 定值文件
    
    // 时间信息
    info.InfoTime.nInfoHappenUtc = 1640995200;
    info.InfoTime.nInfoHappenMs = 0;
    info.InfoTime.nInfoRcvUtc = 1640995200;
    info.InfoTime.nInfoRcvMs = 0;
    
    // 文件名
    info.strWavFileName = fileName;
    
    return info;
}

// 验证PRO_FRAME_BODY的基本字段
void TestUtils::VerifyFrameBodyBasics(const PRO_FRAME_BODY& body, u_int8 expectedType)
{
    EXPECT_EQ(body.nType, expectedType);
    EXPECT_GT(body.nSubstationAdd, 0);
    EXPECT_GT(body.nAddr, 0);
}

// 创建测试文件目录结构
void TestUtils::CreateTestFileStructure(const std::string& basePath)
{
    namespace fs = std::filesystem;
    
    try {
        // 创建基础目录
        fs::create_directories(basePath);
        
        // 创建SettingUp目录结构
        std::string settingUpPath = basePath + "/SettingUp";
        fs::create_directories(settingUpPath);
        
        // 为不同的设备地址创建目录
        for (int addr = 1; addr <= 3; ++addr) {
            std::string devicePath = settingUpPath + "/" + std::to_string(addr);
            fs::create_directories(devicePath);
            
            // 创建一些测试文件
            std::vector<std::string> testFiles = {
                "/setting1.txt",
                "/setting2.cfg",
                "/config.xml"
            };
            
            for (const auto& file : testFiles) {
                std::string filePath = devicePath + file;
                std::ofstream ofs(filePath);
                ofs << "Test content for device " << addr << std::endl;
                ofs.close();
            }
        }
        
        // 创建nxlog目录结构
        std::string nxlogPath = basePath + "/nxlog";
        fs::create_directories(nxlogPath);
        
        // 创建一些日志文件
        std::vector<std::string> logFiles = {
            "/system.log",
            "/error.log",
            "/debug.log"
        };
        
        for (const auto& file : logFiles) {
            std::string filePath = nxlogPath + file;
            std::ofstream ofs(filePath);
            ofs << "Log content" << std::endl;
            ofs.close();
        }
        
        std::cout << "Created test file structure at: " << basePath << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error creating test file structure: " << e.what() << std::endl;
    }
}

// 清理测试文件
void TestUtils::CleanupTestFiles(const std::string& basePath)
{
    namespace fs = std::filesystem;
    
    try {
        if (fs::exists(basePath)) {
            fs::remove_all(basePath);
            std::cout << "Cleaned up test files at: " << basePath << std::endl;
        }
    } catch (const std::exception& e) {
        std::cerr << "Error cleaning up test files: " << e.what() << std::endl;
    }
}
