#include "../include/test_common.h"
#include <memory>

// Mock基类，模拟TNXEcProAsdu的基本功能
class MockTNXEcProAsdu
{
public:
    MockTNXEcProAsdu(MockModelSeek* pSeekIns, MockLogRecord* pLogRecord)
        : m_pModelSeek(pSeekIns), m_pLogRecord(pLogRecord) {}
    
    virtual ~MockTNXEcProAsdu() = default;
    
protected:
    MockModelSeek* m_pModelSeek;
    MockLogRecord* m_pLogRecord;
    u_int8 m_nRii;
    
    // 模拟日志记录函数
    void RcdTrcLogWithParentClass(const char* msg, const char* className) {
        if (m_pLogRecord) {
            m_pLogRecord->RecordTraceLog(msg, className);
        }
    }
    
    void RcdErrLogWithParentClass(const char* msg, const char* className) {
        if (m_pLogRecord) {
            m_pLogRecord->RecordErrorLog(msg, className);
        }
    }
    
    void _SetLogClassName(const char* className) {
        // 模拟设置日志类名
    }
};

// TNXEcProAsdu101GWS的测试实现类
class TestTNXEcProAsdu101GWS : public MockTNXEcProAsdu
{
public:
    TestTNXEcProAsdu101GWS(MockModelSeek* pSeekIns, MockLogRecord* pLogRecord)
        : MockTNXEcProAsdu(pSeekIns, pLogRecord), m_using_area_101(0) {
        _SetLogClassName("TNXEcProAsdu101GWS");
    }
    
    virtual ~TestTNXEcProAsdu101GWS() = default;
    
    // 实现DirectResFromLocal方法（简化版本，重点测试SettingUp逻辑）
    virtual int DirectResFromLocal(IN PRO_FRAME_BODY* pBody, OUT PRO_FRAME_BODY_LIST& lResult)
    {
        if (!pBody) return EC_PRO_CVT_FAIL;
        
        char cError[255] = "";
        RcdTrcLogWithParentClass("通用文件列表召唤处理", "TNXEcProAsdu101GWS");
        
        // 获取文件名
        char cFileName[255] = "";
        if (__GetFileNameFormProFrameBody(pBody, cFileName) < 0) {
            RcdErrLogWithParentClass("DirectResFromLocal:获取报文中的文件名失败", "TNXEcProAsdu101GWS");
            return EC_PRO_CVT_FAIL;
        }
        
        std::string strFullFname = cFileName;
        char* psettingfile = _strstr_nocase((char*)strFullFname.c_str(), "SettingUp");
        
        if (NULL != psettingfile) {
            RcdErrLogWithParentClass("DirectResFromLocal:发现文件名包含SettingUp", "TNXEcProAsdu101GWS");
            
            // 获取返回信息标识符
            m_nRii = (u_int8)pBody->nRii;
            
            // 获取报文中的时间范围
            ASDU_TIME AsduTime;
            if (__GetTimeRangeFromProFrameBody(pBody, AsduTime) < 0) {
                return EC_PRO_CVT_FAIL;
            }
            
            // 提取SettingUp后面的路径部分
            std::string strName = "";
            int npos = strFullFname.find("SettingUp");
            if (npos != std::string::npos) {
                strName = strFullFname.substr(npos + 9); // 得到SettingUp后面的路径部分
            }
            
            // 从通用文件路径下查找相关文件，返回结果
            FILE_PROPERTY_INF_LIST GeneralFileList;
            if (__QueryGeneralFilesList_SettingUp(AsduTime, (char*)strName.c_str(), pBody, GeneralFileList) < 0) {
                return EC_PRO_CVT_FAIL;
            }
            
            // 根据结果生成返回规约的结果报文体
            if (__FormatGeneralFilesListResult(GeneralFileList, lResult) < 0) {
                return EC_PRO_CVT_FAIL;
            }
            
            return 0;
        }
        
        // 其他情况的处理...
        return 0;
    }
    
    // 实现__QueryGeneralFilesList_SettingUp方法
    virtual int __QueryGeneralFilesList_SettingUp(IN ASDU_TIME AsduTime, IN const char* cFindFileName, 
                                                   IN PRO_FRAME_BODY* pBody, OUT FILE_PROPERTY_INF_LIST& GeneralFileList)
    {
        char cError[255] = "";
        
        // 获取路径
        char cGeneralFilePath[500] = "";
        char cTemp[500] = "";
        const char* cFindName = (cFindFileName && strlen(cFindFileName) > 0) ? cFindFileName : "";
        
        // 去数据库配置获得路径
        BASIC_CFG_TB CfgTb;
        if (!m_pModelSeek->GetBasicCfg(CfgTb)) {
            RcdErrLogWithParentClass("__QueryGeneralFilesList_SettingUp:获取系统基本配置表信息失败。", "TNXEcProAsdu101GWS");
            return -1;
        }
        
        if (sy_format_file_path(CfgTb.str_file_path.c_str(), cTemp) < 0) return EC_PRO_CVT_FAIL;
        
        char cFile[255] = "";
        // 拼接路径：基础路径 + "/SettingUp/" + 设备addr地址 + 文件名
        sprintf(cFile, "%s/SettingUp/%d%s", cTemp, pBody->nAddr, cFindName);
        if (sy_format_file_path(cFile, cGeneralFilePath) < 0) return EC_PRO_CVT_FAIL;
        
        sprintf(cError, "__QueryGeneralFilesList_SettingUp:搜索路径[%s],设备addr地址[%d]", cGeneralFilePath, pBody->nAddr);
        RcdTrcLogWithParentClass(cError, "TNXEcProAsdu101GWS");
        
        // 模拟文件搜索逻辑
        // 这里简化实现，实际应该遍历目录查找符合时间范围的文件
        FILE_PROPERTY_INF fileInfo;
        fileInfo.strFileName = "setting1.txt";
        fileInfo.nFileSize = 1024;
        fileInfo.nCreateTime = AsduTime.nInfoHappenUtc;
        fileInfo.nModifyTime = AsduTime.nInfoHappenUtc;
        GeneralFileList.push_back(fileInfo);
        
        fileInfo.strFileName = "setting2.cfg";
        fileInfo.nFileSize = 2048;
        GeneralFileList.push_back(fileInfo);
        
        return 0;
    }
    
private:
    int m_using_area_101;
    
    // 辅助方法：从协议帧体中获取文件名
    int __GetFileNameFormProFrameBody(PRO_FRAME_BODY* pBody, char* fileName)
    {
        if (!pBody || !fileName) return -1;
        
        if (pBody->vVarData.size() < 115) {
            return -1;
        }
        
        // 模拟从可变数据中提取文件名（假设从字节20开始，最大64字节）
        memset(fileName, 0, 255);
        size_t nameLen = std::min(size_t(64), pBody->vVarData.size() - 20);
        memcpy(fileName, &pBody->vVarData[20], nameLen);
        
        // 确保字符串以null结尾
        fileName[nameLen] = '\0';
        
        return 0;
    }
    
    // 辅助方法：从协议帧体中获取时间范围
    int __GetTimeRangeFromProFrameBody(PRO_FRAME_BODY* pBody, ASDU_TIME& asduTime)
    {
        if (!pBody) return -1;
        
        if (pBody->vVarData.size() < 16) {
            return -1;
        }
        
        // 模拟从可变数据中提取时间范围
        memcpy(&asduTime.nInfoHappenUtc, &pBody->vVarData[0], 4);
        memcpy(&asduTime.nInfoRcvUtc, &pBody->vVarData[8], 4);
        asduTime.nInfoHappenMs = 0;
        asduTime.nInfoRcvMs = 0;
        
        return 0;
    }
    
    // 辅助方法：格式化文件列表结果
    int __FormatGeneralFilesListResult(const FILE_PROPERTY_INF_LIST& fileList, PRO_FRAME_BODY_LIST& lResult)
    {
        // 为每个文件创建一个响应帧体
        for (const auto& fileInfo : fileList) {
            PRO_FRAME_BODY responseBody;
            responseBody.nType = 0x65; // ASDU101类型
            responseBody.nVsq = 0x81;
            responseBody.nCot = 0x07; // 激活确认
            responseBody.nSubstationAdd = 1;
            responseBody.nAddr = 1;
            responseBody.nRii = m_nRii;
            
            // 构造响应数据
            std::string fileName = fileInfo.strFileName;
            responseBody.vVarData.resize(fileName.length() + 16); // 文件名 + 文件信息
            
            // 复制文件名
            memcpy(&responseBody.vVarData[0], fileName.c_str(), fileName.length());
            
            // 复制文件大小和时间信息
            memcpy(&responseBody.vVarData[fileName.length()], &fileInfo.nFileSize, 4);
            memcpy(&responseBody.vVarData[fileName.length() + 4], &fileInfo.nCreateTime, 4);
            memcpy(&responseBody.vVarData[fileName.length() + 8], &fileInfo.nModifyTime, 4);
            
            lResult.push_back(responseBody);
        }
        
        return 0;
    }
};

// 测试类
class TNXEcProAsdu101GWSTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        mockModelSeek = std::make_unique<MockModelSeek>();
        mockLogRecord = std::make_unique<MockLogRecord>();
        testAsdu101 = std::make_unique<TestTNXEcProAsdu101GWS>(mockModelSeek.get(), mockLogRecord.get());
        
        // 创建测试文件结构
        TestUtils::CreateTestFileStructure("/tmp/test_files");
    }
    
    void TearDown() override
    {
        // 清理测试文件
        TestUtils::CleanupTestFiles("/tmp/test_files");
    }
    
    std::unique_ptr<MockModelSeek> mockModelSeek;
    std::unique_ptr<MockLogRecord> mockLogRecord;
    std::unique_ptr<TestTNXEcProAsdu101GWS> testAsdu101;
};

// 测试DirectResFromLocal方法处理SettingUp关键字
TEST_F(TNXEcProAsdu101GWSTest, DirectResFromLocal_SettingUp_Success)
{
    // 准备测试数据
    PRO_FRAME_BODY testBody = TestUtils::CreateTestFrameBody(1, "SettingUp/test");
    PRO_FRAME_BODY_LIST result;
    
    // 设置Mock期望
    BASIC_CFG_TB cfg;
    cfg.str_file_path = "/tmp/test_files";
    EXPECT_CALL(*mockModelSeek, GetBasicCfg(::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgReferee<0>(cfg), ::testing::Return(true)));
    
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(1));
    
    EXPECT_CALL(*mockLogRecord, RecordErrorLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(1));
    
    // 执行测试
    int ret = testAsdu101->DirectResFromLocal(&testBody, result);
    
    // 验证结果
    EXPECT_EQ(ret, 0);
    EXPECT_GT(result.size(), 0);
    
    // 验证返回的帧体基本信息
    for (const auto& body : result) {
        TestUtils::VerifyFrameBodyBasics(body, 0x65);
        EXPECT_EQ(body.nCot, 0x07); // 激活确认
    }
}

// 测试__QueryGeneralFilesList_SettingUp方法
TEST_F(TNXEcProAsdu101GWSTest, QueryGeneralFilesList_SettingUp_Success)
{
    // 准备测试数据
    ASDU_TIME testTime;
    testTime.nInfoHappenUtc = 1640995200;
    testTime.nInfoRcvUtc = 1672531200;
    
    PRO_FRAME_BODY testBody = TestUtils::CreateTestFrameBody(1);
    FILE_PROPERTY_INF_LIST fileList;
    
    // 设置Mock期望
    BASIC_CFG_TB cfg;
    cfg.str_file_path = "/tmp/test_files";
    EXPECT_CALL(*mockModelSeek, GetBasicCfg(::testing::_))
        .WillOnce(::testing::DoAll(::testing::SetArgReferee<0>(cfg), ::testing::Return(true)));
    
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(1));
    
    // 执行测试
    int ret = testAsdu101->__QueryGeneralFilesList_SettingUp(testTime, "/test", &testBody, fileList);
    
    // 验证结果
    EXPECT_EQ(ret, 0);
    EXPECT_GT(fileList.size(), 0);
    
    // 验证文件信息
    for (const auto& fileInfo : fileList) {
        EXPECT_FALSE(fileInfo.strFileName.empty());
        EXPECT_GT(fileInfo.nFileSize, 0);
        EXPECT_GT(fileInfo.nCreateTime, 0);
    }
}

// 测试配置获取失败的情况
TEST_F(TNXEcProAsdu101GWSTest, QueryGeneralFilesList_SettingUp_ConfigFail)
{
    // 准备测试数据
    ASDU_TIME testTime;
    PRO_FRAME_BODY testBody = TestUtils::CreateTestFrameBody(1);
    FILE_PROPERTY_INF_LIST fileList;
    
    // 设置Mock期望 - 配置获取失败
    EXPECT_CALL(*mockModelSeek, GetBasicCfg(::testing::_))
        .WillOnce(::testing::Return(false));
    
    EXPECT_CALL(*mockLogRecord, RecordErrorLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(1));
    
    // 执行测试
    int ret = testAsdu101->__QueryGeneralFilesList_SettingUp(testTime, "/test", &testBody, fileList);
    
    // 验证结果
    EXPECT_EQ(ret, -1);
    EXPECT_EQ(fileList.size(), 0);
}

// 测试非SettingUp文件名的处理
TEST_F(TNXEcProAsdu101GWSTest, DirectResFromLocal_NonSettingUp)
{
    // 准备测试数据 - 不包含SettingUp关键字
    PRO_FRAME_BODY testBody = TestUtils::CreateTestFrameBody(1, "normal_file.txt");
    PRO_FRAME_BODY_LIST result;
    
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(::testing::_, ::testing::_))
        .Times(::testing::AtLeast(1));
    
    // 执行测试
    int ret = testAsdu101->DirectResFromLocal(&testBody, result);
    
    // 验证结果 - 应该走其他处理分支
    EXPECT_EQ(ret, 0);
}
