#pragma once

#include "zexuan/base/singleton_registry.hpp"
#include "zexuan/logger.hpp"
#include "zexuan/config_loader.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/base/dynamic_library_loader.hpp"
#include "zexuan/plugin/plugin_base.hpp"
#include "zexuan/base/noncopyable.hpp"
#include <thread>
#include <chrono>
#include <iostream>
#include <vector>
#include <memory>

namespace zexuan {

/**
 * 系统初始化器 - 自动注册核心单例和管理插件生命周期
 */
class SystemInitializer {
public:
    /**
     * 初始化系统单例注册表和插件系统
     * @param configPath 配置文件路径，默认为 "config/config.json"
     */
    static void initialize(const std::string& configPath = "config/config.json");

    /**
     * 清理系统和插件
     */
    static void cleanup();

    /**
     * 获取已加载的插件列表
     * @return 插件列表
     */
    static const std::vector<std::shared_ptr<plugin::PluginBase>>& getLoadedPlugins();

    /**
     * 根据插件ID获取插件实例
     * @param pluginId 插件ID
     * @return 插件实例，如果不存在返回 nullptr
     */
    static std::shared_ptr<plugin::PluginBase> getPlugin(int pluginId);

private:
    // 插件管理相关的私有方法
    static void loadPluginsFromConfig(const std::string& configPath);
    static std::shared_ptr<plugin::PluginBase> loadPlugin(const std::string& libraryPath, int pluginId, const std::string& description);
    static void initializeAllPlugins();
    static void shutdownAllPlugins();

    // 静态成员变量
    static std::vector<std::shared_ptr<plugin::PluginBase>> loadedPlugins_;
    static bool initialized_;
};

/**
 * RAII风格的系统管理器
 */
class SystemManager : public noncopyable {
public:
    SystemManager() {
        SystemInitializer::initialize();
    }

    ~SystemManager() {
        SystemInitializer::cleanup();
    }
};

} // namespace zexuan