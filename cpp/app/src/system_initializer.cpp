#include "system_initializer.hpp"
#include "zexuan/logger.hpp"
#include <stdexcept>
#include <nlohmann/json.hpp>

namespace zexuan {

// 静态成员变量定义
std::vector<std::shared_ptr<plugin::PluginBase>> SystemInitializer::loadedPlugins_;
bool SystemInitializer::initialized_ = false;

void SystemInitializer::initialize(const std::string& configPath) {
    auto logger = Logger::getFileLogger("system_initializer");

    if (initialized_) {
        logger->info("Already initialized");
        return;
    }

    logger->info("Starting simplified system initialization...");

    auto& registry = SingletonRegistry::getInstance();

    try {
        // 1. 只注册 BaseMediator 单例（插件需要共享同一个 mediator 实例）
        logger->info("Registering BaseMediator singleton...");
        auto mediator = registry.get<base::BaseMediator>();
        if (!mediator->initialize()) {
            throw std::runtime_error("Failed to initialize BaseMediator");
        }
        logger->info("BaseMediator registered and initialized");

        // 2. 加载插件
        logger->info("Loading plugins from config...");
        loadPluginsFromConfig(configPath);

        // 3. 初始化所有插件
        initializeAllPlugins();

        initialized_ = true;
        logger->info("System initialization completed successfully!");
        logger->info("Loaded {} plugins", loadedPlugins_.size());

    } catch (const std::exception& e) {
        logger->error("Initialization failed: {}", e.what());
        cleanup();  // 清理已创建的资源
        throw;
    }
}

void SystemInitializer::cleanup() {
    auto logger = Logger::getFileLogger("system_initializer");

    if (!initialized_) {
        return;
    }

    logger->info("Starting system cleanup...");

    try {
        // 1. 关闭所有插件
        shutdownAllPlugins();

        // 2. 清理插件列表
        loadedPlugins_.clear();

        // 3. DynamicLibraryLoader 会在其析构函数中自动卸载动态库
        // BaseMediator 会在 SingletonRegistry 析构时自动清理

        initialized_ = false;
        logger->info("System cleanup completed");

    } catch (const std::exception& e) {
        logger->error("Cleanup error: {}", e.what());
    }
}

const std::vector<std::shared_ptr<plugin::PluginBase>>& SystemInitializer::getLoadedPlugins() {
    return loadedPlugins_;
}

std::shared_ptr<plugin::PluginBase> SystemInitializer::getPlugin(int pluginId) {
    for (const auto& plugin : loadedPlugins_) {
        if (plugin && plugin->getPluginId() == pluginId) {
            return plugin;
        }
    }
    return nullptr;
}

void SystemInitializer::loadPluginsFromConfig(const std::string& configPath) {
    auto logger = Logger::getFileLogger("system_initializer");

    // 创建 ConfigLoader 实例
    ConfigLoader config;
    config.loadFromFile(configPath);

    logger->info("Loading plugins from configuration: {}", configPath);

    // 获取插件配置数组
    auto pluginsJson = config.get<nlohmann::json>("plugins", nlohmann::json::array());

    if (pluginsJson.empty()) {
        throw std::runtime_error("No plugins found in configuration file: " + configPath);
    }

    logger->info("Found {} plugins to load", pluginsJson.size());

    // 遍历插件配置
    for (const auto& pluginConfig : pluginsJson) {
        try {
            // 检查插件是否启用
            bool enabled = pluginConfig.value("enabled", true);
            if (!enabled) {
                logger->info("Skipping disabled plugin");
                continue;
            }

            // 获取插件配置
            int pluginId = pluginConfig.value("id", 0);
            std::string description = pluginConfig.value("description", "");
            std::string libraryPath = pluginConfig.value("library_path", "");

            // 验证必需字段
            if (pluginId == 0) {
                logger->error("✗ Plugin configuration missing id");
                continue;
            }
            if (description.empty()) {
                logger->error("✗ Plugin configuration missing description");
                continue;
            }
            if (libraryPath.empty()) {
                logger->error("✗ Plugin configuration missing library_path");
                continue;
            }

            logger->info("Loading plugin {} ({}) from {}", pluginId, description, libraryPath);

            auto plugin = loadPlugin(libraryPath, pluginId, description);
            if (plugin) {
                loadedPlugins_.push_back(plugin);
                logger->info("✓ Successfully loaded plugin {} ({}) from {}", pluginId, description, libraryPath);
            }

        } catch (const std::exception& e) {
            logger->error("✗ Failed to load plugin: {}", e.what());
            // 插件加载失败时继续加载其他插件，但会记录错误
        }
    }

    if (loadedPlugins_.empty()) {
        throw std::runtime_error("Failed to load any plugins from configuration");
    }
}

std::shared_ptr<plugin::PluginBase> SystemInitializer::loadPlugin(const std::string& libraryPath, int pluginId, const std::string& description) {
    // 使用 DynamicLibraryLoader 加载动态库
    static base::DynamicLibraryLoader loader;  // 静态实例，确保库在程序结束前不被卸载

    if (!loader.loadLibrary(libraryPath)) {
        throw std::runtime_error("Cannot load library: " + loader.getLastError());
    }

    // 获取插件创建函数
    typedef plugin::PluginBase* (*create_plugin_func)(void*, int, const char*);
    auto create_plugin = loader.getFunction<create_plugin_func>(libraryPath, "create_plugin");

    if (!create_plugin) {
        throw std::runtime_error("Cannot load symbol 'create_plugin': " + loader.getLastError());
    }

    // 获取共享的 mediator 实例
    auto mediator = SingletonRegistry::getInstance().get<base::BaseMediator>();

    // 创建插件实例 - 传递指向智能指针的指针
    plugin::PluginBase* plugin_ptr = create_plugin(&mediator, pluginId, description.c_str());
    if (!plugin_ptr) {
        throw std::runtime_error("Failed to create plugin instance");
    }

    // 获取销毁函数（可选）
    typedef void (*destroy_plugin_func)(plugin::PluginBase*);
    auto destroy_plugin = loader.getFunction<destroy_plugin_func>(libraryPath, "destroy_plugin");

    // 返回智能指针包装的插件实例
    return std::shared_ptr<plugin::PluginBase>(plugin_ptr, [destroy_plugin](plugin::PluginBase* p) {
        // 自定义删除器：使用插件的销毁函数或普通删除
        if (p) {
            if (destroy_plugin) {
                destroy_plugin(p);
            } else {
                delete p;  // 回退到普通删除
            }
        }
    });
}

void SystemInitializer::initializeAllPlugins() {
    auto logger = Logger::getFileLogger("system_initializer");
    logger->info("Initializing all plugins...");

    for (auto& plugin : loadedPlugins_) {
        if (plugin) {
            try {
                if (plugin->initialize()) {
                    logger->info("Plugin {} initialized successfully", plugin->getPluginId());
                } else {
                    logger->error("Plugin {} initialization failed", plugin->getPluginId());
                }
            } catch (const std::exception& e) {
                logger->error("Plugin {} initialization error: {}", plugin->getPluginId(), e.what());
            }
        }
    }
}

void SystemInitializer::shutdownAllPlugins() {
    auto logger = Logger::getFileLogger("system_initializer");
    logger->info("Shutting down all plugins...");

    for (auto& plugin : loadedPlugins_) {
        if (plugin) {
            try {
                plugin->shutdown();
                logger->info("Plugin {} shutdown successfully", plugin->getPluginId());
            } catch (const std::exception& e) {
                logger->error("Plugin {} shutdown error: {}", plugin->getPluginId(), e.what());
            }
        }
    }
}

} // namespace zexuan
