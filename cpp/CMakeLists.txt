cmake_minimum_required(VERSION 3.15)
project(zexuan VERSION 1.0.0)

# 使用 ccache 加速编译
set_property(GLOBAL PROPERTY RULE_LAUNCH_COMPILE ccache)
set_property(GLOBAL PROPERTY RULE_LAUNCH_LINK ccache)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/libs)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/libs)

# 查找公共依赖
find_package(spdlog REQUIRED)
find_package(nlohmann_json REQUIRED)
find_package(SOCI REQUIRED)
find_package(libuuid REQUIRED)
find_package(GTest REQUIRED)

# 添加子项目
add_subdirectory(core)
add_subdirectory(interface)
add_subdirectory(plugins)
add_subdirectory(app)

# 可选子项目
option(BUILD_EXAMPLES "Build example projects" OFF)
option(BUILD_TESTS "Build test projects" OFF)

# 显示构建选项状态
message(STATUS "Build configuration:")
message(STATUS "  BUILD_EXAMPLES: ${BUILD_EXAMPLES}")
message(STATUS "  BUILD_TESTS: ${BUILD_TESTS}")

# 根据选项添加子项目
if(BUILD_EXAMPLES)
    message(STATUS "Adding example projects...")
    add_subdirectory(example)
endif()

if(BUILD_TESTS)
    message(STATUS "Adding test projects...")
    add_subdirectory(test)
endif() 