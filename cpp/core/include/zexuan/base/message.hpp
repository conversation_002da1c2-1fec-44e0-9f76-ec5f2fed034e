/**
 * @file message.hpp
 * @brief Message structure based on IEC 60870-5-103 protocol
 * <AUTHOR> from observer.hpp
 * @date 2024
 *
 * Contains message structure following IEC 60870-5-103 (国网103) protocol specification.
 */

#ifndef ZEXUAN_BASE_MESSAGE_HPP
#define ZEXUAN_BASE_MESSAGE_HPP

#include <cstdint>
#include <vector>
#include <string>

namespace zexuan {
namespace base {

/**
 * @brief Message structure based on IEC 60870-5-103 protocol
 *
 * This class implements the ASDU (Application Service Data Unit) structure
 * according to IEC 60870-5-103 protocol specification used in Chinese power grid.
 *
 * Complete Message Structure:
 * - START: Start character 0x68 (启动字符)
 * - LENGTH_L: Length low byte (长度低字节)
 * - LENGTH_H: Length high byte (长度高字节)
 * - TYP: Type Identification (类型标识)
 * - VSQ: Variable Structure Qualifier (可变结构限定词)
 * - COT: Cause of Transmission (传送原因)
 * - ASDU_ADDR: ASDU Address (ASDU地址)
 * - FUN: Function Type (功能类型)
 * - INF: Information Number (信息序号)
 * - Variable Structure: Additional data based on message type
 *
 * Note: START character and LENGTH bytes are automatically managed internally.
 */
class Message {
public:
    /**
     * @brief Default constructor
     */
    Message();

    /**
     * @brief Constructor with basic parameters
     * @param typ Type identification (类型标识)
     * @param vsq Variable structure qualifier (可变结构限定词)
     * @param cot Cause of transmission (传送原因)
     * @param source Source address (源地址)
     * @param fun Function type (功能类型)
     * @param inf Information number (信息序号)
     */
    Message(uint8_t typ, uint8_t vsq, uint8_t cot, uint8_t source,
            uint8_t fun, uint8_t inf);

    /**
     * @brief Get type identification (类型标识)
     */
    uint8_t getTyp() const { return typ_; }

    /**
     * @brief Set type identification (类型标识)
     */
    void setTyp(uint8_t typ) { typ_ = typ; }

    /**
     * @brief Get variable structure qualifier (可变结构限定词)
     */
    uint8_t getVsq() const { return vsq_; }

    /**
     * @brief Set variable structure qualifier (可变结构限定词)
     */
    void setVsq(uint8_t vsq) { vsq_ = vsq; }

    /**
     * @brief Get cause of transmission (传送原因)
     */
    uint8_t getCot() const { return cot_; }

    /**
     * @brief Set cause of transmission (传送原因)
     */
    void setCot(uint8_t cot) { cot_ = cot; }

    /**
     * @brief Get source address (source地址)
     */
    uint8_t getSource() const { return source_; }

    /**
     * @brief Set source address (ASDU地址)
     */
    void setSource(uint8_t Source) { source_ = Source; }

    /**
     * @brief Get target address (ASDU地址)
     */
    uint8_t getTarget() const { return target_; }

    /**
     * @brief Set target address (ASDU地址)
     */
    void setTarget(uint8_t Target) { target_ = Target; }

    /**
     * @brief Get function type (功能类型)
     */
    uint8_t getFun() const { return fun_; }

    /**
     * @brief Set function type (功能类型)
     */
    void setFun(uint8_t fun) { fun_ = fun; }

    /**
     * @brief Get information number (信息序号)
     */
    uint8_t getInf() const { return inf_; }

    /**
     * @brief Set information number (信息序号)
     */
    void setInf(uint8_t inf) { inf_ = inf; }

    /**
     * @brief Get variable structure data (可变结构体)
     */
    const std::vector<uint8_t>& getVariableStructure() const { return variableStructure_; }

    /**
     * @brief Set variable structure data (可变结构体)
     */
    void setVariableStructure(const std::vector<uint8_t>& data) {
        variableStructure_ = data;
        updateLengthBytes();
    }

    /**
     * @brief Add data to variable structure (添加可变结构体数据)
     */
    void addVariableData(uint8_t data) {
        variableStructure_.push_back(data);
        updateLengthBytes();
    }

    /**
     * @brief Clear variable structure data (清空可变结构体数据)
     */
    void clearVariableStructure() {
        variableStructure_.clear();
        updateLengthBytes();
    }

    /**
     * @brief Set text content in variable structure (设置文本内容到可变结构体)
     * @param text Text content to store
     */
    void setTextContent(const std::string& text) {
        variableStructure_.clear();
        variableStructure_.assign(text.begin(), text.end());
        updateLengthBytes();
    }

    /**
     * @brief Get text content from variable structure (从可变结构体获取文本内容)
     * @return Text content as string
     */
    std::string getTextContent() const {
        if (variableStructure_.empty()) {
            return "";
        }
        return std::string(variableStructure_.begin(), variableStructure_.end());
    }

    /**
     * @brief Append text to variable structure (追加文本到可变结构体)
     * @param text Text to append
     */
    void appendText(const std::string& text) {
        variableStructure_.insert(variableStructure_.end(), text.begin(), text.end());
        updateLengthBytes();
    }

    /**
     * @brief Get total message size including all fields (including START and LENGTH bytes)
     */
    size_t getMessageSize() const;

    /**
     * @brief Get ASDU data size (excluding START and LENGTH bytes)
     */
    size_t getAsduSize() const;

    /**
     * @brief Serialize complete message to byte array (including START and LENGTH bytes)
     * @param buffer Output buffer
     * @return Number of bytes written
     */
    size_t serialize(std::vector<uint8_t>& buffer) const;

    /**
     * @brief Deserialize complete message from byte array (including START and LENGTH bytes)
     * @param buffer Input buffer
     * @param offset Starting offset in buffer
     * @return Number of bytes read, 0 if failed
     */
    size_t deserialize(const std::vector<uint8_t>& buffer, size_t offset = 0);

private:
    /**
     * @brief Update length bytes based on current ASDU size
     */
    void updateLengthBytes();

    static constexpr uint8_t START_CHAR = 0x68;    ///< Start character (启动字符)

    uint8_t lengthL_;                           ///< Length low byte (长度低字节)
    uint8_t lengthH_;                           ///< Length high byte (长度高字节)
    uint8_t typ_;                               ///< Type identification (类型标识)
    uint8_t vsq_;                               ///< Variable structure qualifier (可变结构限定词)
    uint8_t cot_;                               ///< Cause of transmission (传送原因)
    uint8_t source_;                            ///< Source address (源地址)
    uint8_t target_;                            ///< Target address (目标地址)
    uint8_t fun_;                               ///< Function type (功能类型)
    uint8_t inf_;                               ///< Information number (信息序号)
    std::vector<uint8_t> variableStructure_;    ///< Variable structure data (可变结构体)
};

} // namespace base
} // namespace zexuan

#endif // ZEXUAN_BASE_MESSAGE_HPP
