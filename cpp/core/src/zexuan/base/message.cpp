/**
 * @file message.cpp
 * @brief Message implementation based on IEC 60870-5-103 protocol
 * <AUTHOR> from observer.cpp
 * @date 2024
 */

#include "../../include/zexuan/base/message.hpp"
#include <fstream>
#include <sstream>
#include <cctype>

namespace zexuan {
namespace base {

// Message implementation

Message::Message()
    : lengthL_(0), lengthH_(0), typ_(0), vsq_(0), cot_(0), source_(0), target_(0), fun_(0), inf_(0) {
    updateLengthBytes();
}

Message::Message(uint8_t typ, uint8_t vsq, uint8_t cot, uint8_t source,
                uint8_t fun, uint8_t inf)
    : lengthL_(0), lengthH_(0), typ_(typ), vsq_(vsq), cot_(cot), source_(source), target_(0), fun_(fun), inf_(inf) {
    updateLengthBytes();
}

size_t Message::getMessageSize() const {
    // Complete message size: START(1) + LENGTH_L(1) + LENGTH_H(1) + ASDU data
    return 3 + getAsduSize();
}

size_t Message::getAsduSize() const {
    // ASDU size: TYP(1) + VSQ(1) + COT(1) + SOURCE(1) + TARGET(1) + FUN(1) + INF(1) + variable structure
    return 7 + variableStructure_.size();
}

size_t Message::serialize(std::vector<uint8_t>& buffer) const {
    size_t totalSize = getMessageSize();
    buffer.clear();
    buffer.reserve(totalSize);

    // Serialize START character and LENGTH bytes
    buffer.push_back(START_CHAR);
    buffer.push_back(lengthL_);
    buffer.push_back(lengthH_);

    // Serialize ASDU fixed header fields
    buffer.push_back(typ_);
    buffer.push_back(vsq_);
    buffer.push_back(cot_);
    buffer.push_back(source_);
    buffer.push_back(target_);
    buffer.push_back(fun_);
    buffer.push_back(inf_);

    // Serialize variable structure
    buffer.insert(buffer.end(), variableStructure_.begin(), variableStructure_.end());

    return totalSize;
}

size_t Message::deserialize(const std::vector<uint8_t>& buffer, size_t offset) {
    if (buffer.size() < offset + 10) {
        return 0; // Not enough data for START + LENGTH + minimum ASDU header
    }

    size_t pos = offset;

    // Check START character
    if (buffer[pos] != START_CHAR) {
        return 0; // Invalid start character
    }
    pos++;

    // Deserialize LENGTH bytes
    lengthL_ = buffer[pos++];
    lengthH_ = buffer[pos++];

    // Calculate expected ASDU length
    uint16_t asduLength = (static_cast<uint16_t>(lengthH_) << 8) | lengthL_;

    // Check if we have enough data for the complete ASDU
    if (buffer.size() < pos + asduLength) {
        return 0; // Not enough data for complete ASDU
    }

    // Deserialize ASDU fixed header fields
    typ_ = buffer[pos++];
    vsq_ = buffer[pos++];
    cot_ = buffer[pos++];
    source_ = buffer[pos++];
    target_ = buffer[pos++];
    fun_ = buffer[pos++];
    inf_ = buffer[pos++];

    // Deserialize variable structure
    variableStructure_.clear();
    size_t variableSize = asduLength - 7; // ASDU length minus fixed header size
    if (variableSize > 0) {
        variableStructure_.assign(buffer.begin() + pos, buffer.begin() + pos + variableSize);
        pos += variableSize;
    }

    return pos - offset;
}

void Message::updateLengthBytes() {
    // 长度字节只计算 ASDU 数据的长度，不包括 START_CHAR 和长度字节本身
    // ASDU 数据包括：TYP + VSQ + COT + SOURCE + TARGET + FUN + INF + variableStructure
    uint16_t asduSize = static_cast<uint16_t>(getAsduSize());
    lengthL_ = static_cast<uint8_t>(asduSize & 0xFF);        // 低字节
    lengthH_ = static_cast<uint8_t>((asduSize >> 8) & 0xFF); // 高字节
}

bool Message::loadFromHexFile(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        return false;
    }

    std::string line;
    std::vector<uint8_t> buffer;

    // 读取文件内容
    while (std::getline(file, line)) {
        std::istringstream iss(line);
        std::string hexByte;

        // 解析每个十六进制字节
        while (iss >> hexByte) {
            // 移除可能的0x前缀
            if (hexByte.length() >= 2 && hexByte.substr(0, 2) == "0x") {
                hexByte = hexByte.substr(2);
            }

            // 检查是否为有效的十六进制字符串
            if (hexByte.length() != 2) {
                continue;
            }

            bool isValid = true;
            for (char c : hexByte) {
                if (!std::isxdigit(c)) {
                    isValid = false;
                    break;
                }
            }

            if (isValid) {
                uint8_t byte = static_cast<uint8_t>(std::stoul(hexByte, nullptr, 16));
                buffer.push_back(byte);
            }
        }
    }

    file.close();

    // 尝试反序列化
    if (buffer.empty()) {
        return false;
    }

    return deserialize(buffer) > 0;
}

// 文本处理方法的实现已经在头文件中内联实现

} // namespace base
} // namespace zexuan
