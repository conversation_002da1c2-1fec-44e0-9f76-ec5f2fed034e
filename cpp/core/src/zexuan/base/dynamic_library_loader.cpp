#include "zexuan/base/dynamic_library_loader.hpp"
#include "zexuan/logger.hpp"
#include <iostream>

namespace zexuan {
namespace base {

DynamicLibraryLoader::~DynamicLibraryLoader() {
    // 自动卸载所有已加载的库
    unloadAllLibraries();
}

bool DynamicLibraryLoader::loadLibrary(const std::string& libPath) {
    // 检查是否已经加载
    if (loadedLibraries_.find(libPath) != loadedLibraries_.end()) {
        lastError_.clear();
        return true;  // 已经加载，返回成功
    }

    LibraryHandle handle = nullptr;

#ifdef _WIN32
    handle = LoadLibraryA(libPath.c_str());
    if (!handle) {
        DWORD errorCode = GetLastError();
        lastError_ = "Failed to load library: " + libPath + " (Error: " + std::to_string(errorCode) + ")";
        return false;
    }
#else
    handle = dlopen(libPath.c_str(), RTLD_LAZY);
    if (!handle) {
        const char* error = dlerror();
        lastError_ = "Failed to load library: " + libPath + " (" + (error ? error : "Unknown error") + ")";
        return false;
    }
#endif

    // 保存库句柄
    loadedLibraries_[libPath] = handle;
    lastError_.clear();

    auto logger = Logger::getFileLogger("dynamic_library_loader");
    logger->info("Successfully loaded library: {}", libPath);
    return true;
}

void DynamicLibraryLoader::unloadAllLibraries() {
    auto logger = Logger::getFileLogger("dynamic_library_loader");

    if (loadedLibraries_.empty()) {
        return;
    }

    logger->info("Unloading {} libraries...", loadedLibraries_.size());

    for (auto& pair : loadedLibraries_) {
        LibraryHandle handle = pair.second;

#ifdef _WIN32
        FreeLibrary(handle);
#else
        dlclose(handle);
#endif
    }

    loadedLibraries_.clear();
    lastError_.clear();
    logger->info("All libraries unloaded");
}

} // namespace base
} // namespace zexuan
