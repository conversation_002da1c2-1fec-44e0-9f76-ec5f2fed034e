#include "zexuan/plugin/plugin_base.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/utils/file_utils.hpp"
#include "zexuan/thread_pool.hpp"
#include "zexuan/logger.hpp"
#include <filesystem>
#include <vector>
#include <algorithm>
#include <iostream>
#include <future>
#include <mutex>
#include <atomic>
#include <thread>
namespace fs = std::filesystem;
using namespace zexuan;
/**
 * Comic Plugin - 多线程批量重命名文件插件
 * 功能：使用线程池并行处理文件夹下的所有文件重命名
 * 格式：基于文件创建时间 + UUID，如：20240823133804_c87398b4-0e7e-4ef5-9d8f-7e2dbde914cc.jpg
 */
class ComicPlugin : public zexuan::plugin::PluginBase {
private:
    std::unique_ptr<zexuan::ThreadPool> threadPool_;
    std::mutex resultMutex_;
    std::atomic<int> processedFiles_{0};
    std::atomic<int> successfulRenames_{0};
    std::atomic<int> failedRenames_{0};

public:
    ComicPlugin(std::shared_ptr<zexuan::base::Mediator> mediator, int pluginId, const std::string& description)
        : PluginBase(mediator, pluginId, description),
          threadPool_(std::make_unique<zexuan::ThreadPool>(32)) {  
    }

    bool initialize() override {
        auto logger = Logger::getFileLogger("comic_plugin");
        logger->info("ComicPlugin {} initializing...", getPluginId());
        return true;
    }

    void shutdown() override {
        auto logger = Logger::getFileLogger("comic_plugin");
        logger->info("ComicPlugin {} shutting down...", getPluginId());
    }

protected:
    void processMessage(const zexuan::base::Message& message) override {
        auto logger = Logger::getFileLogger("comic_plugin");
        logger->debug("ComicPlugin {} received message: TYP={}, COT={}, Source={}, Target={}",
                     getPluginId(),
                     static_cast<int>(message.getTyp()),
                     static_cast<int>(message.getCot()),
                     static_cast<int>(message.getSource()),
                     static_cast<int>(message.getTarget()));

        // 处理文件重命名消息
        if (message.getFun() == 0x01 && message.getCot() == 0x06) { // File rename operation
            handleFileRenameMessage(message);
        } else {
            logger->info("ComicPlugin {} received unknown message type", getPluginId());
        }
    }

private:
    // === 核心功能方法 ===

    /**
     * @brief 处理文件重命名消息
     * @param message 包含目录路径的消息
     */
    void handleFileRenameMessage(const zexuan::base::Message& message) {
        auto logger = Logger::getFileLogger("comic_plugin");

        // 从消息的 variableStructure_ 中提取目录路径
        std::string directoryPath = message.getTextContent();

        if (directoryPath.empty()) {
            logger->error("No directory path provided in message");
            return;
        }

        logger->info("ComicPlugin {} processing directory: {}", getPluginId(), directoryPath);

        // 使用多线程处理目录
        processDirectoryMultiThreaded(directoryPath);
    }

    bool processFolder(const std::string& folderPath) {
        auto logger = Logger::getFileLogger("comic_plugin");
        logger->info("ComicPlugin {} processing folder: {}", getPluginId(), folderPath);

        if (!fs::exists(folderPath) || !fs::is_directory(folderPath)) {
            logger->error("Invalid folder path: {}", folderPath);
            return false;
        }

        // 重置计数器（这个方法现在主要用于兼容性，实际使用多线程方法）
        processedFiles_ = 0;
        successfulRenames_ = 0;
        failedRenames_ = 0;

        try {
            // 收集所有文件
            std::vector<fs::path> files;
            for (const auto& entry : fs::directory_iterator(folderPath)) {
                if (entry.is_regular_file()) {
                    files.push_back(entry.path());
                }
            }

            logger->info("Found {} files to process", files.size());

            // 处理每个文件（单线程版本，保留用于兼容性）
            for (const auto& filePath : files) {
                if (renameFile(filePath)) {
                    successfulRenames_++;
                } else {
                    failedRenames_++;
                }
                processedFiles_++;
            }

            logger->info("Successfully processed {}/{} files", successfulRenames_.load(), files.size());
            return true;

        } catch (const std::exception& e) {
            logger->error("Error during processing: {}", e.what());
            return false;
        }
    }

    bool renameFile(const fs::path& filePath) {
        try {
            std::string newName = generateNewFileName(filePath.string());
            fs::path newPath = filePath.parent_path() / newName;

            // 确保新文件名不存在
            int counter = 1;
            while (fs::exists(newPath)) {
                std::string baseName = newName.substr(0, newName.find_last_of('.'));
                std::string extension = newName.substr(newName.find_last_of('.'));
                newPath = filePath.parent_path() / (baseName + "_" + std::to_string(counter) + extension);
                counter++;
            }

            // 重命名文件
            fs::rename(filePath, newPath);
            auto logger = Logger::getFileLogger("comic_plugin");
            logger->info("Renamed {} -> {}", filePath.filename().string(), newPath.filename().string());
            return true;

        } catch (const std::exception& e) {
            auto logger = Logger::getFileLogger("comic_plugin");
            logger->error("Failed to rename {}: {}", filePath.filename().string(), e.what());
            return false;
        }
    }

    // === 辅助方法 ===
    
    std::string generateNewFileName(const std::string& filePath) {
        // 使用 FileUtils 基于文件创建时间生成文件名
        return zexuan::utils::FileUtils::generateFileNameFromCreationTime(filePath);
    }

    /**
     * @brief 多线程处理目录中的文件重命名
     * @param directoryPath 要处理的目录路径
     */
    void processDirectoryMultiThreaded(const std::string& directoryPath) {
        auto logger = Logger::getFileLogger("comic_plugin");

        // 检查目录是否存在
        if (!fs::exists(directoryPath) || !fs::is_directory(directoryPath)) {
            logger->error("Invalid directory path: {}", directoryPath);
            return;
        }

        // 重置计数器
        processedFiles_ = 0;
        successfulRenames_ = 0;
        failedRenames_ = 0;

        // 收集所有文件
        std::vector<fs::path> files;
        try {
            for (const auto& entry : fs::directory_iterator(directoryPath)) {
                if (entry.is_regular_file()) {
                    files.push_back(entry.path());
                }
            }
        } catch (const fs::filesystem_error& e) {
            logger->error("Error reading directory: {}", e.what());
            return;
        }

        if (files.empty()) {
            logger->info("No files found in directory: {}", directoryPath);
            return;
        }
        // 提交所有文件重命名任务到线程池
        std::vector<std::future<void>> futures;
        futures.reserve(files.size());

        for (const auto& filePath : files) {
            auto future = threadPool_->commit([this, filePath]() {
                renameFileThreadSafe(filePath);
            });
            futures.push_back(std::move(future));
        }

        // 等待所有任务完成
        for (auto& future : futures) {
            try {
                future.get();
            } catch (const std::exception& e) {
                logger->error("Task execution error: {}", e.what());
            }
        }

        // 输出处理结果
        logger->info("Processing completed!");
        logger->info("  Total files: {}", files.size());
        logger->info("  Processed: {}", processedFiles_.load());
        logger->info("  Successful renames: {}", successfulRenames_.load());
        logger->info("  Failed renames: {}", failedRenames_.load());
    }
    /**
     * @brief 线程安全的文件重命名方法
     * @param filePath 要重命名的文件路径
     */
    void renameFileThreadSafe(const fs::path& filePath) {
        try {
            // 基于文件创建时间生成新的文件名
            std::string newFileName = generateNewFileName(filePath.string());

            // 构建新的完整路径
            fs::path newPath = filePath.parent_path() / newFileName;

            // 确保新文件名不存在（处理极少数UUID冲突情况）
            int counter = 1;
            while (fs::exists(newPath)) {
                std::string baseName = newFileName.substr(0, newFileName.find_last_of('.'));
                std::string ext = newFileName.substr(newFileName.find_last_of('.'));
                newPath = filePath.parent_path() / (baseName + "_" + std::to_string(counter) + ext);
                counter++;
            }

            // 执行文件重命名
            fs::rename(filePath, newPath);

            // 线程安全地更新计数器和输出日志
            {
                std::lock_guard<std::mutex> lock(resultMutex_);
                successfulRenames_++;
                auto logger = Logger::getFileLogger("comic_plugin");
                std::ostringstream thread_id_stream;
                thread_id_stream << std::this_thread::get_id();
                logger->info("ComicPlugin [Thread {}]: Renamed: {} -> {}",
                           thread_id_stream.str(),
                           filePath.filename().string(),
                           newPath.filename().string());
            }

        } catch (const fs::filesystem_error& e) {
            // 线程安全地更新失败计数器和输出错误日志
            {
                std::lock_guard<std::mutex> lock(resultMutex_);
                failedRenames_++;
                auto logger = Logger::getFileLogger("comic_plugin");
                logger->error("ComicPlugin [Thread {}]: Failed to rename {}: {}",
                            std::this_thread::get_id(),
                            filePath.filename().string(),
                            e.what());
            }
        } catch (const std::exception& e) {
            {
                std::lock_guard<std::mutex> lock(resultMutex_);
                failedRenames_++;
                auto logger = Logger::getFileLogger("comic_plugin");
                logger->error("ComicPlugin [Thread {}]: Unexpected error renaming {}: {}",
                            std::this_thread::get_id(),
                            filePath.filename().string(),
                            e.what());
            }
        }

        // 更新处理计数
        processedFiles_++;
    }
};

// === 插件导出函数 ===
extern "C" {
    zexuan::plugin::PluginBase* create_plugin(void* mediator, int pluginId, const char* description) {
        auto med = static_cast<std::shared_ptr<zexuan::base::Mediator>*>(mediator);
        return new ComicPlugin(*med, pluginId, std::string(description));
    }

    void destroy_plugin(zexuan::plugin::PluginBase* plugin) {
        delete plugin;
    }

    const char* get_plugin_info() {
        return "Comic Plugin v1.0.0 - Using new PluginBase architecture";
    }
}
